import axios from "axios"
import { BASE_URL, TIMEOUT } from "./config"
class AbiyRequest {
    constructor(baseURL,timeout){
        this.instance = axios.create({
            baseURL,
            timeout
        })
        this.instance.interceptors.request.use(config=>{
            return config
        })
        this.instance.interceptors.response.use(res=>{
            return res.data
        },err=>{
            return err
        })
    }
    request(config){
        return this.instance.request(config)
    }
    get(config){
        return this.request({...config,method:"get"})
    }
}

export default new AbiyRequest(BASE_URL,TIMEOUT)